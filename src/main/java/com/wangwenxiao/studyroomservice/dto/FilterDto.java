package com.wangwenxiao.studyroomservice.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "查询条件")
public class FilterDto {

  @Schema(description = "字段名", example = "username")
  private String field;

  @Schema(description = "操作符: eq, ne, gt, lt, gte, lte, cn, nc, between", example = "eq")
  private String op;

  @Schema(description = "值", example = "admin")
  private Object value;
}
