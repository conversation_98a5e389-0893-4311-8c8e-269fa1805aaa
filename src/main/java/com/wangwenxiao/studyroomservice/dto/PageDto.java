package com.wangwenxiao.studyroomservice.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "分页查询参数")
public class PageDto {

  @Schema(description = "查询条件列表")
  private List<FilterDto> filters;

  @Schema(description = "当前页码", defaultValue = "1")
  private Long pageIndex = 1L;

  @Schema(description = "每页条数", defaultValue = "10")
  private Long pageSize = 10L;
}
