package com.wangwenxiao.studyroomservice.modules.admin.convert;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wangwenxiao.studyroomservice.modules.admin.entity.Admin;
import com.wangwenxiao.studyroomservice.modules.admin.vo.AdminVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface AdminConvertMappers {
  /**
   * 	实例化
   */
  AdminConvertMappers MAPPER = Mappers.getMapper(AdminConvertMappers.class);

  Page<AdminVo> convertPage(Page<Admin> admins);
}
