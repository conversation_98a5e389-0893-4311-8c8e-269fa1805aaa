package com.wangwenxiao.studyroomservice.modules.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wangwenxiao.studyroomservice.common.result.Result;
import com.wangwenxiao.studyroomservice.dto.PageDto;
import com.wangwenxiao.studyroomservice.modules.admin.convert.AdminConvertMappers;
import com.wangwenxiao.studyroomservice.modules.admin.entity.Admin;
import com.wangwenxiao.studyroomservice.modules.admin.mapper.AdminMapper;
import com.wangwenxiao.studyroomservice.modules.admin.service.IAdminService;
import com.wangwenxiao.studyroomservice.modules.admin.vo.AdminVo;
import com.wangwenxiao.studyroomservice.util.QueryParamParser;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class AdminServiceImpl implements IAdminService {
  @Resource
  private AdminMapper adminMapper;

  @Override
  public Result<List<Admin>> getAllAdmin() {
    List<Admin> adminList = adminMapper.selectList(null);

    return Result.success(adminList);
  }

  @Override
  public Result<Page<AdminVo>> getAdminList(PageDto pageDto) {
    Page<Admin> page = new Page<>(pageDto.getPageIndex(), pageDto.getPageSize());

    QueryWrapper<Admin> queryWrapper = QueryParamParser.parse(pageDto.getFilters());
    queryWrapper.eq("deleted", 0);

    Page<Admin> admins = adminMapper.selectPage(page, queryWrapper);
    Page<AdminVo> adminVoList = AdminConvertMappers.MAPPER.convertPage(admins);

    return Result.success(adminVoList);
  }
}
