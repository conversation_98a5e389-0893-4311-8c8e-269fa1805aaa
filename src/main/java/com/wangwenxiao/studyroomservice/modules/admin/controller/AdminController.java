package com.wangwenxiao.studyroomservice.modules.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wangwenxiao.studyroomservice.common.result.Result;
import com.wangwenxiao.studyroomservice.dto.PageDto;
import com.wangwenxiao.studyroomservice.modules.admin.entity.Admin;
import com.wangwenxiao.studyroomservice.modules.admin.service.IAdminService;
import com.wangwenxiao.studyroomservice.modules.admin.vo.AdminVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/admin")
@Tag(name = "管理员")
public class AdminController {

  @Resource
  private IAdminService adminService;

  @GetMapping("/all")
  @Operation(summary = "获取所有管理员")
  public Result<List<Admin>> getAllAdmin() {
    return adminService.getAllAdmin();
  }

  @PostMapping("/list")
  @Operation(summary = "分页获取管理员")
  public Result<Page<AdminVo>> getAdminList(@RequestBody PageDto pageDto) {
    return adminService.getAdminList(pageDto);
  }
}
