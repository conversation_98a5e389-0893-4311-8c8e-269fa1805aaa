package com.wangwenxiao.studyroomservice.modules.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("tb_admins")
public class Admin {

  @TableId(type = IdType.AUTO)
  private Integer id;

  private String username;

  private String password;

  private String realName;

  private Integer roleId;

  private String email;

  private String phone;

  private String remark;

  private LocalDateTime createTime;

  private LocalDateTime updateTime;

  private Boolean deleted;
}
