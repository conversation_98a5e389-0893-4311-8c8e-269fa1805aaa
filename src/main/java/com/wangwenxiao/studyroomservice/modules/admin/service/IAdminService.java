package com.wangwenxiao.studyroomservice.modules.admin.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wangwenxiao.studyroomservice.common.result.Result;
import com.wangwenxiao.studyroomservice.dto.PageDto;
import com.wangwenxiao.studyroomservice.modules.admin.entity.Admin;
import com.wangwenxiao.studyroomservice.modules.admin.vo.AdminVo;

import java.util.List;

public interface IAdminService {
  // 获取管理员列表
  Result<List<Admin>> getAllAdmin();

  // 分页获取管理员列表
  Result<Page<AdminVo>> getAdminList(PageDto pageDto);
}
