package com.wangwenxiao.studyroomservice.common.result;

import lombok.Data;

/**
 * 统一返回结果封装
 */
@Data
public class Result<T> {

  private Integer code; // 状态码
  private Boolean success; // 是否成功
  private T data; // 数据
  private String message; // 返回消息

  private static <T> Result<T> build(Integer code, Boolean success, T data, String message) {
    Result<T> r = new Result<>();
    r.setCode(code);
    r.setSuccess(success);
    r.setData(data);
    r.setMessage(message);
    return r;
  }

  // ===== 静态方法快速构造 =====
  public static <T> Result<T> success(T data) {
    return build(200, true, data, null);
  }

  public static <T> Result<T> success(T data, String message) {
    return build(200, true, data, message);
  }

  public static <T> Result<T> failure(String message) {
    return build(200, false, null, message);
  }

  public static <T> Result<T> failure(Integer code, String message) {
    return build(code, false, null, message);
  }
}
