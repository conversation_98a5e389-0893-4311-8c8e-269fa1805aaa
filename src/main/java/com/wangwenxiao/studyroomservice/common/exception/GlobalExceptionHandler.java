package com.wangwenxiao.studyroomservice.common.exception;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.HashMap;
import java.util.Map;

/**
 * 全局异常处理器
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

  @ExceptionHandler(Exception.class)
  public ResponseEntity<Map<String, Object>> handleException(HttpServletRequest request, Exception e) {
    HttpStatusCode status = HttpStatus.INTERNAL_SERVER_ERROR;

    if(e instanceof org.springframework.web.server.ResponseStatusException) {
      status = ((org.springframework.web.server.ResponseStatusException) e).getStatusCode();
    }

    Map<String, Object> body = new HashMap<>();
    body.put("code", status.value());
    body.put("success", false);
    body.put("data", null);
    body.put("message", e.getMessage() != null ? e.getMessage() : "服务器错误");

    return new ResponseEntity<>(body, status);
  }
}
