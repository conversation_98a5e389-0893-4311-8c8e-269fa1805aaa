package com.wangwenxiao.studyroomservice.util;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wangwenxiao.studyroomservice.dto.FilterDto;
import com.wangwenxiao.studyroomservice.dto.PageDto;

import java.util.List;

/**
 * 通用分页查询参数解析器
 */
public class QueryParamParser {
  /**
   * 解析分页查询的 filters 参数为 QueryWrapper
   *
   * @param filters 查询条件列表
   * @param <T>     实体类型
   * @return QueryWrapper<T>
   */
  public static <T> QueryWrapper<T> parse(List<FilterDto> filters) {
    QueryWrapper<T> wrapper = new QueryWrapper<>();

    if (filters != null) {
      for (FilterDto filter : filters) {
        String field = filter.getField();
        String op = filter.getOp();
        Object value = filter.getValue();

        switch (op) {
          case "eq":
            wrapper.eq(field, value);
            break;
          case "ne":
            wrapper.ne(field, value);
            break;
          case "gt":
            wrapper.gt(field, value);
            break;
          case "lt":
            wrapper.lt(field, value);
            break;
          case "gte":
            wrapper.ge(field, value);
            break;
          case "lte":
            wrapper.le(field, value);
            break;
          case "cn": // contains
            wrapper.like(field, value);
            break;
          case "nc": // not contains
            wrapper.notLike(field, value);
            break;
          case "between":
            if (value instanceof List<?> && ((List<?>) value).size() == 2) {
              wrapper.between(field, ((List<?>) value).get(0), ((List<?>) value).get(1));
            }
            break;
          default:
            throw new IllegalArgumentException("不支持的操作符: " + op);
        }
      }
    }

    return wrapper;
  }
}
